import React, { useState, useEffect } from "react";
import { useConversationStore } from "../stores/conversation-store";
import { useUserStore } from "../stores/user-store";
import { SidebarProvider } from "./ui/sidebar";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { MessageSquare, Plus, Trash2, Edit3, Check, X, Menu, Loader2 } from "lucide-react";
import { cn } from "../lib/utils";

interface ConversationSidebarProps {
	children: React.ReactNode;
	// 外部控制侧边栏状态的props
	isCollapsed?: boolean;
	onToggle?: () => void;
}

export function ConversationSidebar({
	children,
	isCollapsed: externalIsCollapsed,
	onToggle,
}: ConversationSidebarProps) {
	const {
		conversations,
		currentConversationId,
		createConversation,
		deleteConversation,
		updateConversationTitle,
		setCurrentConversation,
		loadUserConversations,
		isLoading,
		error,
	} = useConversationStore();

	const { user } = useUserStore();
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editTitle, setEditTitle] = useState("");
	// 侧边栏收起/展开状态 - 支持外部控制
	const [internalIsCollapsed, setInternalIsCollapsed] = useState(false);
	// 内容隐藏状态，用于实现更流畅的动画效果
	const [contentHidden, setContentHidden] = useState(false);
	// 检测是否为移动端
	const [isMobile, setIsMobile] = useState(false);
	const isCollapsed =
		externalIsCollapsed !== undefined
			? externalIsCollapsed
			: internalIsCollapsed;

	const handleCreateConversation = async () => {
		try {
			await createConversation();
			// Optionally refresh the page or navigate to the new conversation
			window.location.reload();
		} catch (error) {
			console.error("创建对话失败:", error);
		}
	};

	const handleDeleteConversation = async (id: string, e: React.MouseEvent) => {
		e.stopPropagation();
		try {
			await deleteConversation(id);
		} catch (error) {
			console.error("删除对话失败:", error);
		}
	};

	const handleEditStart = (
		id: string,
		currentTitle: string,
		e: React.MouseEvent
	) => {
		e.stopPropagation();
		setEditingId(id);
		setEditTitle(currentTitle);
	};

	const handleEditSave = async (id: string) => {
		if (editTitle.trim()) {
			try {
				await updateConversationTitle(id, editTitle.trim());
			} catch (error) {
				console.error("更新标题失败:", error);
			}
		}
		setEditingId(null);
		setEditTitle("");
	};

	const handleEditCancel = () => {
		setEditingId(null);
		setEditTitle("");
	};

	// 切换侧边栏收起/展开状态
	const toggleSidebar = () => {
		if (isCollapsed) {
			// 展开时：先展开宽度，再显示内容
			if (onToggle) {
				onToggle();
			} else {
				setInternalIsCollapsed(false);
			}
			// 延迟显示内容，等待宽度动画完成
			setTimeout(() => {
				setContentHidden(false);
			}, 150);
		} else {
			// 收起时：先隐藏内容，再收起宽度
			setContentHidden(true);
			// 延迟收起宽度，等待内容隐藏动画完成
			setTimeout(() => {
				if (onToggle) {
					onToggle();
				} else {
					setInternalIsCollapsed(true);
				}
			}, 150);
		}
	};

	// 同步内容隐藏状态与侧边栏状态
	useEffect(() => {
		if (isCollapsed) {
			setContentHidden(true);
		} else {
			// 展开时：延迟显示内容，等待宽度动画完成
			setTimeout(() => {
				setContentHidden(false);
			}, 300);
		}
	}, [isCollapsed]);

	// 检测移动端
	useEffect(() => {
		const checkMobile = () => {
			setIsMobile(window.innerWidth < 768);
		};
		
		checkMobile();
		window.addEventListener('resize', checkMobile);
		return () => window.removeEventListener('resize', checkMobile);
	}, []);

	// 移动端默认收起侧边栏
	useEffect(() => {
		if (isMobile && !internalIsCollapsed && externalIsCollapsed === undefined) {
			setInternalIsCollapsed(true);
		}
	}, [isMobile]);

	// 用户登录时加载对话历史
	useEffect(() => {
		if (user) {
			loadUserConversations();
		}
	}, [user, loadUserConversations]);

	// 添加快捷键支持 (Ctrl/Cmd + B)
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "b" && (event.ctrlKey || event.metaKey)) {
				event.preventDefault();
				toggleSidebar();
			}
		};

		window.addEventListener("keydown", handleKeyDown);
		return () => window.removeEventListener("keydown", handleKeyDown);
	}, [isCollapsed]);

	// 移动端点击遮罩关闭侧边栏
	const handleOverlayClick = () => {
		if (isMobile && !isCollapsed) {
			toggleSidebar();
		}
	};

	const formatDate = (date: Date | string) => {
		// 确保date是Date对象
		const dateObj = date instanceof Date ? date : new Date(date);

		// 检查日期是否有效
		if (isNaN(dateObj.getTime())) {
			return "无效日期";
		}

		const now = new Date();
		const diff = now.getTime() - dateObj.getTime();
		const days = Math.floor(diff / (1000 * 60 * 60 * 24));

		if (days === 0) {
			return "今天";
		} else if (days === 1) {
			return "昨天";
		} else if (days < 7) {
			return `${days}天前`;
		} else {
			return dateObj.toLocaleDateString("zh-CN");
		}
	};

	return (
		<SidebarProvider defaultOpen={true}>
			{/* 移动端遮罩层 */}
			{isMobile && !isCollapsed && (
				<div 
					className="fixed inset-0 bg-black/50 z-40 md:hidden"
					onClick={handleOverlayClick}
				/>
			)}
			
			{/* 收缩按钮 - 移动端时显示在右上角 */}
			{isMobile && (
				<Button
					variant="outline"
					size="sm"
					className="fixed top-4 left-4 z-50 md:hidden h-10 w-10 p-0"
					onClick={toggleSidebar}
					aria-label={isCollapsed ? "展开侧边栏" : "收起侧边栏"}
				>
					<Menu className="h-4 w-4" />
				</Button>
			)}
			
			{/* 使用固定宽度的侧边栏容器，移动端时覆盖主内容 */}
			<div className={cn(
				"flex min-h-screen w-full",
				isMobile ? "relative" : ""
			)}>
				{/* 侧边栏区域 */}
				<div
					className={cn(
						"bg-sidebar border-r border-border transition-all duration-300 ease-in-out z-50",
						// 桌面端：固定宽度，不覆盖内容
						!isMobile && "flex-shrink-0",
						!isMobile && (isCollapsed ? "w-0 overflow-hidden" : "w-80"),
						// 移动端：固定定位，覆盖内容
						isMobile && "fixed top-0 left-0 h-full",
						isMobile && (isCollapsed ? "-translate-x-full" : "translate-x-0 w-80")
					)}
				>
					<div className="h-full flex flex-col">
						{/* 侧边栏头部 */}
						<div
							className={cn(
								"border-b px-4 h-16 py-3 transition-opacity duration-150 ease-in-out",
								contentHidden ? "opacity-0" : "opacity-100"
							)}
						>
							<div className="flex items-center justify-between">
								<h2 className="text-lg font-semibold">对话历史</h2>
								<Button
									onClick={handleCreateConversation}
									size="sm"
									variant="outline"
									className="h-8 w-8 p-0"
									disabled={isLoading || !user}
								>
									{isLoading ? (
										<Loader2 className="h-4 w-4 animate-spin" />
									) : (
										<Plus className="h-4 w-4" />
									)}
								</Button>
							</div>
							{error && (
								<div className="mt-2 text-xs text-red-500 bg-red-50 p-2 rounded">
									{error}
								</div>
							)}
						</div>

						{/* 侧边栏内容区域 */}
						<div
							className={cn(
								"flex-1 overflow-auto transition-opacity duration-150 ease-in-out",
								contentHidden ? "opacity-0" : "opacity-100"
							)}
						>
							<div className="p-2">
								{isLoading ? (
									<div className="px-4 py-8 text-center text-sm text-muted-foreground">
										<Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
										加载对话历史...
									</div>
								) : !user ? (
									<div className="px-4 py-8 text-center text-sm text-muted-foreground">
										请先登录
									</div>
								) : conversations.length === 0 ? (
									<div className="px-4 py-8 text-center text-sm text-muted-foreground">
										暂无对话历史
									</div>
								) : (
									<div className="space-y-1">
										{conversations.map((conversation) => (
											<div
												key={conversation.id}
												className={cn(
													"group relative flex items-center gap-3 rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",
													currentConversationId === conversation.id &&
														"bg-accent"
												)}
												onClick={() => setCurrentConversation(conversation.id)}
											>
												<MessageSquare className="h-4 w-4 flex-shrink-0" />
												<div className="flex-1 min-w-0">
													{editingId === conversation.id ? (
														<div
															className="flex items-center gap-1"
															onClick={(e) => e.stopPropagation()}
														>
															<Input
																value={editTitle}
																onChange={(e) => setEditTitle(e.target.value)}
																className="h-6 text-xs"
																onKeyDown={(e) => {
																	if (e.key === "Enter") {
																		handleEditSave(conversation.id);
																	} else if (e.key === "Escape") {
																		handleEditCancel();
																	}
																}}
																autoFocus
															/>
															<Button
																size="sm"
																variant="ghost"
																className="h-6 w-6 p-0"
																onClick={() => handleEditSave(conversation.id)}
															>
																<Check className="h-3 w-3" />
															</Button>
															<Button
																size="sm"
																variant="ghost"
																className="h-6 w-6 p-0"
																onClick={handleEditCancel}
															>
																<X className="h-3 w-3" />
															</Button>
														</div>
													) : (
														<>
															<div className="font-medium truncate">
																{conversation.title}
															</div>
															<div className="text-xs text-muted-foreground">
																{formatDate(conversation.updatedAt)} •{" "}
																{conversation.messages.length} 条消息
															</div>
														</>
													)}
												</div>
												{editingId !== conversation.id && (
													<div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
														<Button
															size="sm"
															variant="ghost"
															className="h-6 w-6 p-0"
															onClick={(e) =>
																handleEditStart(
																	conversation.id,
																	conversation.title,
																	e
																)
															}
														>
															<Edit3 className="h-3 w-3" />
														</Button>
														<Button
															size="sm"
															variant="ghost"
															className="h-6 w-6 p-0 text-destructive hover:text-destructive"
															onClick={(e) =>
																handleDeleteConversation(conversation.id, e)
															}
														>
															<Trash2 className="h-3 w-3" />
														</Button>
													</div>
												)}
											</div>
										))}
									</div>
								)}
							</div>
						</div>

						{/* 侧边栏底部 */}
						<div
							className={cn(
								"border-t px-4 py-3 transition-opacity duration-150 ease-in-out",
								contentHidden ? "opacity-0" : "opacity-100"
							)}
						>
							<div className="text-xs text-muted-foreground">
								共 {conversations.length} 个对话
							</div>
						</div>
					</div>
				</div>

				{/* 主内容区域 - 占据剩余空间 */}
				<main className={cn(
					"flex-1 flex flex-col min-w-0 w-full relative",
					// 移动端时占据全宽
					isMobile && "w-full"
				)}>
					{children}
				</main>
			</div>
		</SidebarProvider>
	);
}
